import 'dart:ui';

import 'package:neorevv/src/core/config/app_strings.dart';

import 'agent.dart';

class BrokerList {
  String id;
  String name;
  double totalSales;
  double totalCommission;
  String imageUrl;
  String contact;
  String email;
  String address;
  int agents;
  DateTime joinDate;
  double totalSalesVolume;

  BrokerList({
    required this.id,
    required this.name,
    required this.imageUrl,
    required this.contact,
    required this.email,
    required this.address,
    required this.agents,
    required this.totalCommission,
    required this.joinDate, // Add joinDate field
    this.totalSalesVolume = 0.0,
    this.totalSales = 0.0,
  });

  factory BrokerList.fromJson(Map<String, dynamic> json) {
    return BrokerList(
      id: json[brokerListIdKey] as String,
      name: json[brokerListNameKey] as String,
      imageUrl: json[brokerListImageUrlKey] as String,
      contact: json[brokerListContactKey] as String,
      email: json[brokerListEmailKey] as String,
      address: json[brokerListAddressKey] as String,
      agents: json[brokerListAgentsKey] as int,
      totalSales: json[brokerListtotalSalesVolume] as double,
      totalCommission: json[brokerListTotalCommissionKey] as double,
      joinDate: DateTime.parse(
        json[brokerListJoinDateKey] as String,
      ),
      totalSalesVolume: (json['totalSalesVolume'] ?? 0.0).toDouble(),
    );
  }
}
