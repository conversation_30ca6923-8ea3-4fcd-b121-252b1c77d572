import 'dart:convert';
import 'package:flutter/material.dart';
import 'package:flutter_hooks/flutter_hooks.dart';
import 'package:neorevv/src/core/config/app_strings.dart';
import 'package:neorevv/src/core/config/json_consts.dart';
import 'package:neorevv/src/core/config/constants.dart';
import 'package:neorevv/src/core/theme/app_fonts.dart';
import 'package:neorevv/src/core/theme/app_theme.dart';
import 'package:neorevv/src/domain/models/agent.dart';

import 'package:neorevv/src/presentation/screens/dashboard/components/brokers_table.dart';
import 'package:neorevv/src/presentation/shared/components/tables/CustomDataTableWidget.dart';
import 'package:neorevv/src/presentation/shared/components/action_button_eye.dart';

class AgentsListScreen extends HookWidget {
  const AgentsListScreen({super.key});

  // Method to read sampleagentDataResponse and convert to agent model
  List<Agent> readagentDataFromJson() {
    try {
      // Debug: Print the JSON string length and first few characters

      // Parse the JSON string
      final Map<String, dynamic> jsonData = json.decode(
        sampleAgentDataResponse,
      );

      // Extract agentData array
      final List<dynamic> agentDataList = jsonData[agentDataKey] ?? [];

      // Convert to agent model objects with field mapping
      final List<Agent> agentList = agentDataList.map((item) {
        // Map the JSON fields to match agent model expectations.
        final Map<String, dynamic> mappedItem = Map<String, dynamic>.from(item);

        // Ensure all numeric fields have default values if null using proper agent keys
        mappedItem[agentSalesKey] = mappedItem[agentSalesKey] ?? 0;
        mappedItem[agentAmountKey] = mappedItem[agentAmountKey] ?? 0.0;
        mappedItem[agentCommissionKey] = mappedItem[agentCommissionKey] ?? 0.0;
        mappedItem[agentTotalDealsKey] = mappedItem[agentTotalDealsKey] ?? 0;
        mappedItem[agentEarningKey] = mappedItem[agentEarningKey] ?? 0.0;

        // Ensure all string fields have default values if null
        mappedItem[agentNameKey] = mappedItem[agentNameKey] ?? '';
        mappedItem[agentContactKey] = mappedItem[agentContactKey] ?? '';
        mappedItem[agentEmailKey] = mappedItem[agentEmailKey] ?? '';
        mappedItem[agentStateKey] = mappedItem[agentStateKey] ?? '';
        mappedItem[agentCityKey] = mappedItem[agentCityKey] ?? '';
        mappedItem[agentLevelKey] = mappedItem[agentLevelKey] ?? '';
        mappedItem[agentRelatedBrokerKey] =
            mappedItem[agentRelatedBrokerKey] ?? '';
        mappedItem[agentImageUrlKey] = mappedItem[agentImageUrlKey] ?? '';

        // Set agents to empty list since it's not in JSON anymore
        mappedItem[agentAgentsKey] = <Agent>[];

        // Set default color since it's not in JSON anymore
        mappedItem[agentColorKey] = Colors.blue;

        // Parse joinDate string to DateTime object
        if (mappedItem[agentJoinDateKey] != null &&
            mappedItem[agentJoinDateKey] is String) {
          mappedItem[agentJoinDateKey] = DateTime.parse(
            mappedItem[agentJoinDateKey],
          );
        } else {
          mappedItem[agentJoinDateKey] = DateTime.now();
        }

        // Ensure boolean fields have default values
        mappedItem[agentStatusKey] = mappedItem[agentStatusKey] ?? true;

        return Agent.fromJson(mappedItem);
      }).toList();

      return agentList;
    } catch (e) {
      debugPrint('Error parsing agent data: $e');
      return <Agent>[];
    }
  }

  @override
  Widget build(BuildContext context) {
    // Call the method to read and print agent data from JSON
    final List<Agent> agentData = readagentDataFromJson();

    // Use predefined headers from app_strings.dart
    final List<String> formattedHeaders = [
      agentName,
      agentContact,
      agentEmail,
      agentJoinDate,
      agentState,
      agentCity,
      agentLevel,
      agentTotalDeals,
      agentEarning,
      agentStatus,
    ];

    final sortedAgents = useState<List<Agent>>(agentData);

    void handleSort(String columnName, bool ascending) {
      final sorted = List<Agent>.from(sortedAgents.value);
      sorted.sort((a, b) {
        dynamic aValue, bValue;

        switch (columnName) {
          case agentName:
            aValue = a.name;
            bValue = b.name;
            break;
          case agentContact:
            aValue = a.contact;
            bValue = b.contact;
            break;
          case agentEmail:
            aValue = a.email;
            bValue = b.email;
            break;
          case agentJoinDate:
            aValue = a.joinDate;
            bValue = b.joinDate;
            break;
          case agentState:
            aValue = a.state;
            bValue = b.state;
            break;
          case agentCity:
            aValue = a.city;
            bValue = b.city;
            break;
          case agentLevel:
            aValue = a.level;
            bValue = b.level;
            break;
          case agentTotalDeals:
            aValue = a.totalDeals;
            bValue = b.totalDeals;
            break;
          case agentEarning:
            aValue = a.earning;
            bValue = b.earning;
            break;
          case agentStatus:
            aValue = a.status;
            bValue = b.status;
            break;
          default:
            aValue = '';
            bValue = '';
        }

        final comparison = aValue is num && bValue is num
            ? aValue.compareTo(bValue)
            : aValue is DateTime && bValue is DateTime
            ? aValue.compareTo(bValue)
            : aValue.toString().compareTo(bValue.toString());
        return ascending ? comparison : -comparison;
      });
      sortedAgents.value = sorted;
    }

    return Column(
      mainAxisSize: MainAxisSize.min,
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        //Customized table layout
        CustomDataTableWidget<Agent>(
          data: sortedAgents.value,
          title: agents,
          titleIcon: userIconTablePath,
          searchHint: searchAgent,
          searchFn: (agent) => agent.name + agent.contact + agent.email,
          // Dynamic filtering system
          filterColumnNames: [
            agentName, // name
            agentState, // state
            agentLevel, // level
          ],
          filterValueExtractors: {
            agentName: (agent) => agent.name,
            agentState: (agent) => agent.state,
            agentLevel: (agent) => agent.level,
          },
          columnNames: formattedHeaders,
          cellBuilders: [
            (agent) => agent.name,
            (agent) => agent.contact,
            (agent) => agent.email,
            (agent) =>
                '${agent.joinDate.day}/${agent.joinDate.month}/${agent.joinDate.year}',
            (agent) => agent.state,
            (agent) => agent.city,
            (agent) => agent.level,
            (agent) => agent.totalDeals.toString(),
            (agent) => '₹${agent.earning.toStringAsFixed(2)}',
            (agent) => agent.status ? 'Active' : 'Inactive',
          ],
          // Widget builders for styled cells
          widgetCellBuilders: [
            null, // name - use text
            null, // contact - use text
            null, // email - use text
            null, // joinDate - use text
            null, // state - use text
            null, // city - use text
            null, // level - use text
            null, // totalDeals - use text
            null, // earning - use text
            (context, agent) => Container(
              // status - widget that fills cell width but maintains its own height
              width: double.infinity, // Fill the entire cell width
              // Small margin from cell edges
              padding: const EdgeInsets.symmetric(horizontal: 20, vertical: 8),
              decoration: BoxDecoration(
                color: agent.status
                    ? const Color(0xFFB8E6B8) // Light green background
                    : const Color(0xFFFFB8B8), // Light red/pink background
                borderRadius: BorderRadius.circular(
                  20,
                ), // More rounded for oval shape
              ),
              child: Text(
                agent.status ? 'Active' : 'Inactive',
                style: AppFonts.semiBoldTextStyle(
                  12,
                  color: agent.status
                      ? const Color(0xFF2E7D32)
                      : const Color(0xFFD32F2F),
                ),
                textAlign: TextAlign.center,
              ),
            ),
          ],
          // Boolean flags to indicate which columns use widget builders
          useWidgetBuilders: [
            false, // name - use text
            false, // contact - use text
            false, // email - use text
            false, // joinDate - use text
            false, // state - use text
            false, // city - use text
            false, // level - use text
            false, // totalDeals - use text
            false, // earning - use text
            true, // status - use widget
          ],
          actionBuilders: [
            (context, agent) => ActionButtonEye(
              onPressed: () => _onAgentAction(context, agent),
              isCompact: true,
              isMobile: false,
            ),
          ],

          mobileCardBuilder: (context, agent) =>
              _buildMobileAgentCard(agent, context),
          onSort: handleSort,
          emptyStateMessage: noDataAvailable,
        ),
      ],
    );
  }

  void _onAgentAction(BuildContext context, Agent agent) {
    // Navigate to agent detail or show action
    ScaffoldMessenger.of(
      context,
    ).showSnackBar(SnackBar(content: Text('Action clicked for ${agent.name}')));
  }

  Widget _buildMobileAgentCard(Agent agent, BuildContext context) {
    return Container(
      margin: const EdgeInsets.only(bottom: 12),
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: AppTheme.white,
        borderRadius: BorderRadius.circular(8),
        border: Border.all(color: Colors.grey.shade200),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            mainAxisAlignment: MainAxisAlignment.spaceBetween,
            children: [
              Text(agent.name, style: TextStyle(fontWeight: FontWeight.bold)),
              Container(
                padding: const EdgeInsets.symmetric(
                  horizontal: 16,
                  vertical: 8,
                ),
                decoration: BoxDecoration(
                  color: agent.status
                      ? AppTheme
                            .agentStatusActiveBg // Light green background
                      : AppTheme
                            .agentStatusInactiveBg, // Light red/pink background
                  borderRadius: BorderRadius.circular(
                    20,
                  ), // More rounded for oval shape
                ),
                child: Text(
                  agent.status ? active : inactive,
                  style: TextStyle(
                    color: agent.status
                        ? AppTheme
                              .agentStatusActiveText // Darker green text
                        : AppTheme.agentStatusInactiveText, // Darker red text
                    fontWeight: FontWeight.w600,
                    fontSize: 12,
                  ),
                  textAlign: TextAlign.center,
                ),
              ),
            ],
          ),
          const SizedBox(height: 8),
          Text('$agentContact: ${agent.contact}'),
          Text('$agentEmail: ${agent.email}'),
          Text('$agentState: ${agent.state}'),
          Text('$agentCity: ${agent.city}'),
          Text('$agentLevel: ${agent.level}'),
          Text('$agentTotalDeals: ${agent.totalDeals}'),
          Text('$agentEarning: ₹${agent.earning.toStringAsFixed(2)}'),
          Text(
            '$agentJoinDate: ${agent.joinDate.day}/${agent.joinDate.month}/${agent.joinDate.year}',
          ),
          const SizedBox(height: 12),
          SizedBox(
            width: double.infinity,
            child: ActionButtonEye(
              onPressed: () => _onAgentAction(context, agent),
              isMobile: true,
            ),
          ),
        ],
      ),
    );
  }
}
