import 'package:flutter/material.dart';
import '../../../core/theme/app_theme.dart';
import '../../../core/theme/app_fonts.dart';

class ActionButtonEye extends StatelessWidget {
  final VoidCallback onPressed;
  final bool isCompact;
  final bool isMobile;

  const ActionButtonEye({
    super.key,
    required this.onPressed,
    this.isCompact = false,
    this.isMobile = false,
  });

  @override
  Widget build(BuildContext context) {
    if (isMobile) {
      return _buildMobileButton();
    } else {
      return _buildDesktopButton();
    }
  }

  Widget _buildMobileButton() {
    return SizedBox(
      width: double.infinity,
      height: 40,
      child: ElevatedButton.icon(
        onPressed: onPressed,
        style: ElevatedButton.styleFrom(
          backgroundColor: AppTheme.roundIconColor,
          foregroundColor: Colors.white,
          elevation: 0,
          shape: RoundedRectangleBorder(
            borderRadius: BorderRadius.circular(8),
          ),
          padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 8),
        ),
        icon: const Icon(Icons.visibility, size: 16),
        label: Text(
          'View',
          style: AppFonts.mediumTextStyle(14, color: Colors.white),
        ),
      ),
    );
  }

  Widget _buildDesktopButton() {
    final double buttonSize = isCompact ? 32 : 40;
    final double iconSize = isCompact ? 16 : 20;

    return Material(
      color: Colors.transparent,
      child: InkWell(
        onTap: onPressed,
        borderRadius: BorderRadius.circular(buttonSize / 2),
        child: Container(
          width: buttonSize,
          height: buttonSize,
          decoration: BoxDecoration(
            color: AppTheme.roundIconBgColor,
            shape: BoxShape.circle,
          ),
          child: Icon(
            Icons.visibility,
            size: iconSize,
            color: AppTheme.roundIconColor,
          ),
        ),
      ),
    );
  }
}
