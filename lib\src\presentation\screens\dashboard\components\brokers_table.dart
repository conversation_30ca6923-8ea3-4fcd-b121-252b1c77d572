import 'dart:convert';
import 'package:flutter/material.dart';
import 'package:flutter_hooks/flutter_hooks.dart';
import 'package:neorevv/src/core/config/app_strings.dart';
import 'package:neorevv/src/core/config/constants.dart';
import 'package:neorevv/src/core/config/json_consts.dart';
import 'package:neorevv/src/domain/models/broker_list.dart';
import 'package:neorevv/src/presentation/screens/dashboard/components/brokers_table.dart';
import 'package:neorevv/src/presentation/shared/components/tables/CustomDataTableWidget.dart';
import 'package:neorevv/src/presentation/shared/components/action_button_eye.dart';


class BrokersTable extends HookWidget {
  const BrokersTable({super.key});

  // Helper methods for safe type conversion
  int _toInt(dynamic value) {
    if (value == null) return 0;
    if (value is int) return value;
    if (value is double) return value.toInt();
    if (value is String) return int.tryParse(value) ?? 0;
    return 0;
  }

  double _toDouble(dynamic value) {
    if (value == null) return 0.0;
    if (value is double) return value;
    if (value is int) return value.toDouble();
    if (value is String) return double.tryParse(value) ?? 0.0;
    return 0.0;
  }

  // Method to read broker data and convert to BrokerList model
  List<BrokerList> readBrokerDataFromJson() {
    try {
      // Parse the JSON string.
      final Map<String, dynamic> jsonData = json.decode(
        sampleBrokersDataResponse,
      );

      // Extract salesData array.
      final List<dynamic> brokerDataList = jsonData[brokersDataKey] ?? [];

      // Convert to BrokerList model objects
      final List<BrokerList> brokerList = brokerDataList.map((item) {
        final Map<String, dynamic> mappedItem = Map<String, dynamic>.from(item);

        // Ensure all required String fields have default values if null
        mappedItem[brokerListIdKey] = mappedItem[brokerListIdKey]?.toString() ?? '';
        mappedItem[brokerListNameKey] = mappedItem[brokerListNameKey]?.toString() ?? '';
        mappedItem[brokerListContactKey] = mappedItem[brokerListContactKey]?.toString() ?? '';
        mappedItem[brokerListAddressKey] = mappedItem[brokerListAddressKey]?.toString() ?? '';
        mappedItem[brokerListEmailKey] = mappedItem[brokerListEmailKey]?.toString() ?? '';
        mappedItem[brokerListImageUrlKey] = ''; // Set empty string to avoid null error

        // Ensure numeric fields have default values if null and convert to proper types
        // Map totalSales to sales field (BrokerList expects 'sales' field)
        //mappedItem['sales'] = _toInt(mappedItem[brokerListtotalSalesKey]);

        // Ensure the model's required fields are properly mapped
        mappedItem[brokerListTotalSalesKey] = _toDouble(mappedItem[brokerListTotalSalesKey]);
        mappedItem[brokerListTotalCommissionKey] = _toDouble(mappedItem[brokerListTotalCommissionKey]);

        // Map totalAgents to agents field (BrokerList expects an int for agents count)
        mappedItem[brokerListAgentsKey] = _toInt(mappedItem[brokerListAgentsKey]);

        // Ensure joinDate exists and is properly formatted
        if (mappedItem[brokerListJoinDateKey] == null) {
          mappedItem[brokerListJoinDateKey] = DateTime.now().toIso8601String();
        }

        // Create BrokerList object manually to avoid fromJson issues
        return BrokerList(
          id: mappedItem[brokerListIdKey]?.toString() ?? '',
          name: mappedItem[brokerListNameKey]?.toString() ?? '',
          contact: mappedItem[brokerListContactKey]?.toString() ?? '',
          email: mappedItem[brokerListEmailKey]?.toString() ?? '',
          address: mappedItem[brokerListAddressKey]?.toString() ?? '',
          imageUrl: '',
          agents: _toInt(mappedItem[brokerListAgentsKey]),
          totalSales: _toDouble(mappedItem[brokerListTotalSalesKey]),
          totalCommission: _toDouble(mappedItem[brokerListTotalCommissionKey]),
          joinDate: DateTime.parse(mappedItem[brokerListJoinDateKey] ?? DateTime.now().toIso8601String()),
        );
      }).toList();
      return brokerList;
    } catch (e) {
      debugPrint('Error parsing broker data: $e');
      return <BrokerList>[];
    }
  }

  @override
  Widget build(BuildContext context) {
    // Call the method to read and print sales data from JSON
    final List<BrokerList> brokerData = readBrokerDataFromJson();

    // Use predefined headers from app_strings.dart
    final List<String> formattedHeaders = [
      brokerListNameColumnHeader,
      brokerListContactColumnHeader,
      brokerListEmailColumnHeader,
      brokerListAddressColumnHeader,
      brokerListJoinDateColumnHeader,
      brokerListAgentsColumnHeader,
      brokerListTotalSalesColumnHeader
    ];

    final sortedBrokers = useState<List<BrokerList>>(brokerData);

    void handleSort(String columnName, bool ascending) {
      final sorted = List<BrokerList>.from(sortedBrokers.value);
      sorted.sort((a, b) {
        dynamic aValue, bValue;

        switch (columnName) {
          case brokerListNameColumnHeader:
            aValue = a.name;
            bValue = b.name;
            break;
          case brokerListContactColumnHeader:
            aValue = a.contact;
            bValue = b.contact;
            break;
          case brokerListEmailColumnHeader:
            aValue = a.email;
            bValue = b.email;
            break;
          case brokerListAddressColumnHeader:
            aValue = a.address;
            bValue = b.address;
          break;
          case brokerListJoinDateColumnHeader:
            aValue = a.joinDate;
            bValue = b.joinDate;
            break;
          case brokerListAgentsColumnHeader:
            aValue = a.agents;
            bValue = b.agents;
            break;
          case brokerListSalesColumnHeader:
            aValue = a.totalSales;
            bValue = b.totalSales;
            break;
          default:
            aValue = '';
            bValue = '';
        }

        final comparison = aValue is num && bValue is num
            ? aValue.compareTo(bValue)
            : aValue is DateTime && bValue is DateTime
            ? aValue.compareTo(bValue)
            : aValue.toString().compareTo(bValue.toString());
        return ascending ? comparison : -comparison;
      });
      sortedBrokers.value = sorted;
    }

    return Column(
      mainAxisSize: MainAxisSize.min,
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        //Customized table layout
        CustomDataTableWidget<BrokerList>(
                      data: sortedBrokers.value,
                      title: brokersTab,
                      titleIcon: userIconTablePath,
                      searchHint: searchHint,
                      searchFn: (broker) => broker.name,
                      // Dynamic filtering system
                      filterColumnNames: [
                        brokerListJoinDateColumnHeader, // propertyType
                      ],
                      filterValueExtractors: {
                        brokerListJoinDateColumnHeader: (broker) => '${broker.joinDate.day}/${broker.joinDate.month}/${broker.joinDate.year}',
                      },
                      columnNames: formattedHeaders,
                      cellBuilders: [
                        (broker) => broker.name,
                        (broker) => broker.contact,
                        (broker) => broker.email,
                        (broker) => broker.address,
                        (broker) => '${broker.joinDate.day}/${broker.joinDate.month}/${broker.joinDate.year}',
                        (broker) => broker.agents.toString(),
                        (broker) => '₹${broker.totalSales.toStringAsFixed(2)}',
                      ],
                      actionBuilders: [
                        (context, broker) => ActionButtonEye(
                          onPressed: () => _onBrokerAction(context, broker),
                          isCompact: true,
                          isMobile: false,
                        ),
                      ],

                      mobileCardBuilder: (context, broker) =>
                          _buildMobileBrokerCard(broker, context),
                      onSort: handleSort,
                      emptyStateMessage: noDataAvailable,
                    ),
      ],
    );
  }

  void _onBrokerAction(BuildContext context, BrokerList broker) {
    // Navigate to sales detail or show action
    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(content: Text('Action clicked for ${broker.name}')),
    );
  }

  Widget _buildMobileBrokerCard(BrokerList broker, BuildContext context) {
    return Container(
      margin: const EdgeInsets.only(bottom: 12),
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(8),
        border: Border.all(color: Colors.grey.shade200),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            mainAxisAlignment: MainAxisAlignment.spaceBetween,
            children: [
              Text(
                broker.name,
                style: TextStyle(fontWeight: FontWeight.bold),
              ),
            ],
          ),
          const SizedBox(height: 8),
          Text('$brokerListNameColumnHeader: ${broker.name}'),
          Text('$brokerListContactColumnHeader: ${broker.contact}'),
          Text('$brokerListEmailColumnHeader: ${broker.email}'),
          Text('$brokerListAddressColumnHeader: ${broker.address}'),
          Text(
            '$brokerListJoinDateColumnHeader: ${broker.joinDate.day}/${broker.joinDate.month}/${broker.joinDate.year}',
          ),
           
          Text('$brokerListAgentsColumnHeader: ${broker.agents}'),
          Text(
            '$brokerListSalesColumnHeader: ₹${broker.totalSales.toStringAsFixed(2)}',
          ),
          const SizedBox(height: 12),
          SizedBox(
            width: double.infinity,
            child: ActionButtonEye(
              onPressed: () => _onBrokerAction(context, broker),
              isMobile: true,
            ),
          ),
        ],
      ),
    );
  }
}
