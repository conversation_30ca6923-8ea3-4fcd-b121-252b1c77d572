import 'package:flutter/material.dart';
import 'package:flutter_hooks/flutter_hooks.dart';
import 'package:fl_chart/fl_chart.dart';
import '../../../../core/config/app_strings.dart';
import '../../../../core/config/constants.dart';
import '../../../../domain/models/agent.dart';
import '../../../../domain/models/broker.dart';
import '../../../../core/theme/app_theme.dart';
import '../../../../core/theme/app_fonts.dart';
import '../../../../core/config/responsive.dart';

class SalesByBrokersCard extends HookWidget {
  final List<Broker> brokersList;
  final List<Agent> agentsList;
  final bool isBrokerView;

  const SalesByBrokersCard({
    super.key,
    required this.isBrokerView,
    required this.brokersList,
    required this.agentsList,
  });

  @override
  Widget build(BuildContext context) {
    final touchedIndex = useState<int>(-1);
    final chartSectionsCount = useState<int>(0);

    useEffect(() {
      chartSectionsCount.value = isBrokerView
          ? brokersList.length
          : agentsList.length;
      return null;
    }, [brokersList, agentsList]);

    final Size size = MediaQuery.of(context).size;
    final bool isTablet = Responsive.isTablet(context);

    return Container(
      margin: isTablet
          ? EdgeInsets.fromLTRB(
              0,
              defaultPadding / 1.5,
              defaultPadding / 1.5,
              defaultPadding / 1.5,
            )
          : const EdgeInsets.symmetric(horizontal: defaultPadding / 1.5),
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(12),
        boxShadow: [
          BoxShadow(
            color: Colors.black.withValues(alpha: 0.08),
            blurRadius: 8,
            offset: const Offset(0, 2),
          ),
        ],
      ),
      child: ClipRRect(
        borderRadius: BorderRadius.circular(12),
        child: Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            Padding(
              padding: const EdgeInsets.symmetric(vertical: 8.0),
              child: Text(
                salesTab,
                textAlign: TextAlign.center,
                style: AppFonts.semiBoldTextStyle(
                  16,
                  color: AppTheme.primaryTextColor,
                ),
              ),
            ),
            Expanded(
              child: Responsive(
                smallMobile: _pieChartColumnView(
                  context,
                  size,
                  touchedIndex,
                  chartSectionsCount,
                ),
                mobile: Row(
                  crossAxisAlignment: CrossAxisAlignment.center,
                  mainAxisAlignment: MainAxisAlignment.center,
                  mainAxisSize: MainAxisSize.min,
                  children: [
                    _pieChart(
                      context,
                      size,
                      touchedIndex,
                      chartSectionsCount,
                    ),
                    _pieChartLegend(
                      context,
                      touchedIndex,
                      chartSectionsCount,
                    ),
                  ],
                ),
                tablet: _pieChartColumnView(
                  context,
                  size,
                  touchedIndex,
                  chartSectionsCount,
                ),
                desktop: _pieChartColumnView(
                  context,
                  size,
                  touchedIndex,
                  chartSectionsCount,
                ),
              ),
            ),
          ],
        ),
      ),
    );
  }

  // for desktop, tablet and small mobile views
  Widget _pieChartColumnView(
    BuildContext context,
    Size size,
    ValueNotifier<int> touchedIndex,
    ValueNotifier<int> chartSectionsCount,
  ) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.center,
      mainAxisAlignment: MainAxisAlignment.center,
      mainAxisSize: MainAxisSize.min,
      children: [
        _pieChart(context, size, touchedIndex, chartSectionsCount),
        _pieChartLegend(context, touchedIndex, chartSectionsCount),
      ],
    );
  }

  Widget _pieChart(
    BuildContext context,
    Size size,
    ValueNotifier<int> touchedIndex,
    ValueNotifier<int> chartSectionsCount,
  ) {
    return Expanded(
      flex: 2,
      child: SizedBox(
        height: 700,
        width: 400,
        child: Align(
          alignment: Alignment.center,
          child: Stack(
            children: [
              _buildPieChart(context, size, touchedIndex, chartSectionsCount),
              if (touchedIndex != -1) _chartBadge(touchedIndex),
            ],
          ),
        ),
      ),
    );
  }

  Widget _buildPieChart(
    BuildContext context,
    Size size,
    ValueNotifier<int> touchedIndex,
    ValueNotifier<int> chartSectionsCount,
  ) {
    return PieChart(
      PieChartData(
        pieTouchData: PieTouchData(
          touchCallback: (FlTouchEvent event, pieTouchResponse) {
            //TODO: handle navigation to selected sales info
            if (pieTouchResponse != null &&
                pieTouchResponse.touchedSection != null &&
                pieTouchResponse.touchedSection!.touchedSectionIndex !=
                    touchedIndex.value) {
              if (!event.isInterestedForInteractions) {
                touchedIndex.value = -1;
                return;
              }
              touchedIndex.value =
                  pieTouchResponse.touchedSection!.touchedSectionIndex;
            }
          },
        ),
        borderData: FlBorderData(show: true),
        sectionsSpace: 1.5,
        centerSpaceRadius: 0,
        sections: _showingSections(
          context,
          size,
          touchedIndex,
          chartSectionsCount,
        ),
      ),
    );
  }

  List<PieChartSectionData> _showingSections(
    BuildContext context,
    Size size,
    ValueNotifier<int> touchedIndex,
    ValueNotifier<int> chartSectionsCount,
  ) {
    final isTablet = Responsive.isTablet(context);
    return List.generate(chartSectionsCount.value, (i) {
      final isTouched = i == touchedIndex.value;
      final radius = isTouched
          ? isTablet
                ? 110.0
                : 70.0
          : isTablet
          ? 100.0
          : 65.0;

      return PieChartSectionData(
        showTitle: false,
        borderSide: BorderSide.none,
        color: isBrokerView ? brokersList[i].color : agentsList[i].color,
        value: isBrokerView
            ? brokersList[i].totalCommission.toDouble()
            : agentsList[i].amount.toDouble(),
        title: '', // Remove title text from pie chart
        radius: radius,
        titleStyle: AppFonts.semiBoldTextStyle(0, color: Colors.white),
      );
    });
  }

  Widget _chartBadge(ValueNotifier<int> touchedIndex) {
    if (touchedIndex.value == -1) return const Positioned(child: SizedBox());

    final color = isBrokerView
        ? brokersList[touchedIndex.value].color
        : agentsList.isNotEmpty
        ? agentsList[touchedIndex.value].color
        : Colors.transparent;

    final name = isBrokerView
        ? brokersList[touchedIndex.value].name
        : agentsList.isNotEmpty
        ? agentsList[touchedIndex.value].name
        : '';
    final sales = isBrokerView
        ? brokersList[touchedIndex.value].sales
        : agentsList.isNotEmpty
        ? agentsList[touchedIndex.value].sales
        : 0;
    return Positioned(
      top: 0,
      right: 0,
      child: Container(
        padding: EdgeInsets.all(12),
        decoration: BoxDecoration(
          color: color,
          borderRadius: BorderRadius.circular(8),
          boxShadow: [
            BoxShadow(
              color: Colors.black.withValues(alpha: 0.2),
              blurRadius: 8,
              offset: Offset(0, 2),
            ),
          ],
        ),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          mainAxisSize: MainAxisSize.min,
          children: [
            Text(
              name,
              style: AppFonts.semiBoldTextStyle(11, color: Colors.white),
            ),
            Text(
              '$sales $salesTab',
              style: AppFonts.semiBoldTextStyle(9, color: Colors.white),
            ),
          ],
        ),
      ),
    );
  }

  Widget _pieChartLegend(
    BuildContext context,
    ValueNotifier<int> touchedIndex,
    ValueNotifier<int> chartSectionsCount,
  ) {
    final size = MediaQuery.sizeOf(context);
    final isMobile = Responsive.isMobile(context);
    final isTablet = Responsive.isTablet(context);
    final isDesktop = Responsive.isDesktop(context);
    final isSmallMobile = Responsive.isSmallMobile(context);
    final crossAxisCount = size.width < 300
        ? 1
        : isSmallMobile
        ? 1
        : isTablet
        ? 2
        : 2;
    return Expanded(
      flex: isTablet || (size.width < mobileBreakpoint && size.width > 650)
          ? 2
          : 3,
      child: Container(
        margin: const EdgeInsets.fromLTRB(
          defaultMargin,
          defaultMargin / 2,
          defaultMargin,
          defaultMargin,
        ),
        decoration: BoxDecoration(
          border: Border.all(color: AppTheme.salesLegendBorderColor, width: 1),
          borderRadius: BorderRadius.circular(12),
        ),
        child: ClipRRect(
          borderRadius: BorderRadius.circular(12),
          child: GridView.builder(
            shrinkWrap: true,
            gridDelegate: SliverGridDelegateWithFixedCrossAxisCount(
              crossAxisCount: crossAxisCount,
              childAspectRatio: isMobile
                  ? 6
                  : size.width > 1200 && size.width < 1800 && !isMobile
                  ? isTablet
                        ? 4
                        : 3.3
                  : 4.8,
            ),
            itemCount: chartSectionsCount.value,
            itemBuilder: (context, index) {
              return GestureDetector(
                onTap: () {
                  // TODO: navigate to sales detail screen
                  // Navigator.push(
                  //   context,
                  //   MaterialPageRoute(
                  //     builder: (context) =>
                  //         SalesDetailScreen(salesPerson: salesData[index]),
                  //   ),
                  // );
                },
                child: Container(
                  decoration: BoxDecoration(
                    color: touchedIndex.value == index
                        ? isBrokerView
                              ? brokersList[index].color.withValues(alpha: 0.1)
                              : agentsList.isNotEmpty
                              ? agentsList[index].color.withValues(alpha: 0.1)
                              : Colors.transparent
                        : Colors.transparent,
                    border: Border(
                      bottom: BorderSide(
                        color: AppTheme.salesLegendBorderColor,
                        width: 1,
                      ),
                    ),
                  ),
                  padding: const EdgeInsets.symmetric(
                    horizontal: 8,
                    vertical: 2,
                  ),
                  child: Align(
                    alignment: Alignment.centerLeft,
                    child: Row(
                      mainAxisSize: MainAxisSize.min,
                      crossAxisAlignment: CrossAxisAlignment.center,
                      mainAxisAlignment: MainAxisAlignment.spaceAround,

                      children: [
                        // color square
                        Container(
                          width: 10,
                          height: 10,
                          decoration: BoxDecoration(
                            color: isBrokerView
                                ? brokersList[index].color
                                : agentsList.isNotEmpty
                                ? agentsList[index].color
                                : Colors.transparent,

                            borderRadius: BorderRadius.circular(3),
                          ),
                        ),
                        SizedBox(width: 8),
                        _legendUserInfo(
                          index,
                          isDesktop || isTablet,

                          touchedIndex,
                        ),
                      ],
                    ),
                  ),
                ),
              );
            },
          ),
        ),
      ),
    );
  }

  Widget _legendUserInfo(
    int index,
    bool isDesktop,
    ValueNotifier<int> touchedIndex,
  ) {
    final name = isBrokerView
        ? brokersList[index].name
        : agentsList.isNotEmpty
        ? agentsList[index].name
        : '';
    final sales = isBrokerView
        ? brokersList[index].sales
        : agentsList.isNotEmpty
        ? agentsList[index].sales
        : 0;
    return Expanded(
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          Flexible(
            child: Text(
              name,
              style: AppFonts.mediumTextStyle(
                isDesktop ? 10 : 8,
                color: AppTheme.primaryTextColor,
              ),
              overflow: TextOverflow.ellipsis,
            ),
          ),
          Flexible(
            child: Text(
              '$sales $salesTab',
              style: AppFonts.mediumTextStyle(
                isDesktop ? 8 : 10,
                color: AppTheme.breadcrumbArrowColor,
              ),
            ),
          ),
        ],
      ),
    );
  }
}
