import 'package:flutter/material.dart';
import '../../../domain/models/broker.dart';
import '../../../core/theme/app_fonts.dart';
import '../../../core/theme/app_theme.dart';
import '../../../core/config/json_consts.dart';

class DropdownChip extends StatefulWidget {
  final String label;
  final bool expandText;
  final Color bgColor;
  final Color textColor;
  final VoidCallback? onTap;
  final bool isBrokerSelector;
  final Function(Broker)? onBrokerSelected;

  const DropdownChip({
    Key? key,
    required this.label,
    this.expandText = false,
    this.bgColor = AppTheme.commissionDropDownBgColor,
    this.textColor = Colors.white,
    this.onTap,
    this.isBrokerSelector = false,
    this.onBrokerSelected,
  }) : super(key: key);

  @override
  State<DropdownChip> createState() => _DropdownChipState();
}

class _DropdownChipState extends State<DropdownChip> {
  Broker? _selectedBroker;

  @override
  Widget build(BuildContext context) {
    if (widget.isBrokerSelector) {
      return _buildBrokerDropdown();
    } else {
      return _buildStandardDropdown();
    }
  }

  Widget _buildBrokerDropdown() {
    return Container(
      height: 40,
      padding: const EdgeInsets.symmetric(horizontal: 8),
      decoration: BoxDecoration(
        color: widget.bgColor,
        borderRadius: BorderRadius.circular(20),
      ),
      child: DropdownButtonHideUnderline(
        child: DropdownButton<Broker>(
          menuMaxHeight: 300,
          value: _selectedBroker,
          hint: Padding(
            padding: const EdgeInsets.only(left: 8.0),
            child: Text(
              widget.label,
              style: AppFonts.regularTextStyle(14, color: widget.textColor),
              overflow: TextOverflow.ellipsis,
            ),
          ),
          icon: Icon(
            Icons.keyboard_arrow_down,
            color: widget.textColor,
            size: 16,
          ),
          isExpanded: widget.expandText,
          dropdownColor: AppTheme.scaffoldBgColor,
          borderRadius: BorderRadius.circular(8),
          items: brokersListJson.map((broker) {
            return DropdownMenuItem<Broker>(
              value: broker,
              child: Text(
                broker.name,
                style: AppFonts.regularTextStyle(
                  14,
                  color: AppTheme.primaryTextColor,
                ),
              ),
            );
          }).toList(),
          onChanged: (Broker? broker) {
            if (broker != null && widget.onBrokerSelected != null) {
              widget.onBrokerSelected!(broker);
              setState(() {
                _selectedBroker = broker;
              });
            }
          },
        ),
      ),
    );
  }

  Widget _buildStandardDropdown() {
    return Container(
      height: 40,
      padding: const EdgeInsets.symmetric(horizontal: 8),
      decoration: BoxDecoration(
        color: widget.bgColor,
        borderRadius: BorderRadius.circular(20),
      ),
      child: DropdownButtonHideUnderline(
        child: DropdownButton<String>(
          value: null,
          hint: Text(
            widget.label,
            style: AppFonts.regularTextStyle(12, color: widget.textColor),
            overflow: TextOverflow.ellipsis,
          ),
          icon: Icon(
            Icons.keyboard_arrow_down,
            color: widget.textColor,
            size: 16,
          ),
          isExpanded: widget.expandText,
          dropdownColor: Colors.white,
          borderRadius: BorderRadius.circular(8),
          items: const [], // Add your dropdown items here
          onChanged: (String? value) {
            if (widget.onTap != null) {
              widget.onTap!();
            }
          },
        ),
      ),
    );
  }
}
