import 'package:flutter/material.dart';
import 'package:flutter_hooks/flutter_hooks.dart';
import '../../../core/config/app_strings.dart';
import '../../../core/config/constants.dart';
import '../../../core/config/responsive.dart';
import '../dashboard/components/header.dart';
import 'components/agents_table.dart';

class AgentsScreen extends HookWidget {
  const AgentsScreen({Key? key}) : super(key: key);

  @override
  Widget build(BuildContext context) {
    final header = Header(selectedTab: agentsTab);

    return Scaffold(
      drawer: header.mobileDrawer,
      body: SafeArea(child: const AgentsScreen()),
    );
  }
}
