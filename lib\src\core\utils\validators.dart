import '../config/app_strings.dart';
import 'regex.dart';

class InputValidators {
  InputValidators._();

  static String? validateEmail(String? value) {
    if (value == null || value.isEmpty) return emailIsRequired;
    final emailRegex = RegExUtils.emailRegex;
    if (!emailRegex.hasMatch(value)) return invalidEmail;
    return null;
  }

  static String? validatePhone(String? value) {
    if (value == null || value.isEmpty) {
      return phoneNumberIsRequired;
    }
    // US phone validation: 10 digits (excluding country code)
    // Allow formats like: 1234567890, ************
    final cleanedNumber = value.replaceAll(RegExUtils.nonNumeric, '');
    if (cleanedNumber.length != 10) {
      return invalidPhone;
    }
    return null;
  }

  static String? validatePassword(String? value) {
    if (value == null || value.isEmpty) return passwordIsRequired;
    if (value.length < 6) return passwordMustBeAtLeast6Characters;
    return null;
  }

  static String? validateConfirmPassword(String? value, String password) {
    if (value != password) return passwordsDoNotMatch;
    return null;
  }

  static String? validateRequiredField(String? value) {
    if (value == null || value.isEmpty) return thisFieldIsRequired;
    return null;
  }
}
