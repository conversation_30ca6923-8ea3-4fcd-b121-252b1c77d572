class ApiException implements Exception {
  final String message;
  final int? statusCode;

  ApiException({required this.message, this.statusCode});

  @override
  String toString() => message;
}

class InvalidCredentialsException implements Exception {
  final String message;
  final int? statusCode;

  InvalidCredentialsException({required this.message, this.statusCode});

  @override
  String toString() => message;
}
